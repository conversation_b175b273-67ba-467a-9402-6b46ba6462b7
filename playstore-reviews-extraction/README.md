# Google Play Store Review Extractor

A web application that allows users to extract and analyze reviews from Google Play Store applications. Built with Flask and featuring a responsive dark/light mode interface.

## Features

- Extract reviews from any Google Play Store app using the app ID
- Filter reviews by:
  - Date range
  - Rating (minimum and maximum)
  - Keywords
- Visualize review data with:
  - Rating distribution chart
  - Timeline of review submissions
- Export reviews to Excel
- Dark/Light mode toggle
- Real-time progress tracking
- Responsive design

## Installation

1. Clone the repository:
git clone <repository-url>
cd playstore_extraction

2. Create and activate a conda environment:
conda create --name playstore_reviews_extraction python=3.9
conda activate playstore_reviews_extraction

3. Install the required packages:
pip install -r requirements.txt

## Usage

1. Start the Flask server:
python app.py

2. Open your web browser and navigate to:
http://localhost:5000

3. Enter an app ID (e.g., "com.example.app") and set optional filters:
   - Date range
   - Rating range
   - Keywords (comma-separated)

4. Click "Get Reviews" to start the extraction process

5. Export the results to Excel using the "Export to Excel" button

## Project Structure
playstore_extraction/
├── static/
│ ├── css/
│ │ └── style.css
│ └── js/
│ └── theme.js
├── templates/
│ └── index.html
├── app.py
├── requirements.txt
└── README.md

## Dependencies

- Flask
- google-play-scraper
- pandas
- numpy
- xlsxwriter
- matplotlib
- seaborn
- jupyter
- textblob
- nltk
- wordcloud
- scikit-learn
- plotly
- gunicorn

## Finding App ID

To find an app's ID:
1. Go to the app's Google Play Store page
2. The ID is in the URL after "id="
   Example: `https://play.google.com/store/apps/details?id=com.example.app`
   App ID would be: `com.example.app`

## Contributing

Feel free to submit issues, fork the repository, and create pull requests for any improvements.
