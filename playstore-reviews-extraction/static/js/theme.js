// Check for saved theme preference, otherwise use system preference
const prefersDarkScheme = window.matchMedia('(prefers-color-scheme: dark)');
const currentTheme = localStorage.getItem('theme');

function setTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    document.body.setAttribute('data-theme', theme);
    localStorage.setItem('theme', theme);
}

if (currentTheme) {
    setTheme(currentTheme);
    if (currentTheme === 'dark') {
        document.getElementById('checkbox').checked = true;
    }
} else {
    if (prefersDarkScheme.matches) {
        setTheme('dark');
        document.getElementById('checkbox').checked = true;
    } else {
        setTheme('light');
    }
}

// Listen for toggle switch change
document.getElementById('checkbox').addEventListener('change', function(e) {
    if (e.target.checked) {
        setTheme('dark');
    } else {
        setTheme('light');
    }
});
