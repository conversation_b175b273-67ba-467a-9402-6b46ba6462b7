# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Google Play Store Review Extractor - a Flask web application that scrapes and analyzes reviews from Google Play Store applications. The app provides filtering, visualization, and export capabilities with a responsive dark/light mode interface.

## Development Commands

### Environment Setup
```bash
# Create and activate conda environment
conda create --name playstore_reviews_extraction python=3.9
conda activate playstore_reviews_extraction

# Install dependencies
pip install -r requirements.txt
```

### Running the Application
```bash
# Start the Flask development server
python app.py
```
The server automatically tries ports 5001-5010 until it finds an available one (skips 5000 due to AirPlay conflicts).

### Testing
```bash
# Test basic API endpoint (adjust port as needed)
curl http://localhost:5001/test
```

## Architecture

### Core Components

**app.py** - Main Flask application with the following key patterns:
- Stream-based review extraction using Server-Sent Events (SSE) for real-time progress
- Custom exception handling for different error types (AppNotFoundError, NetworkError, etc.)
- Batch processing of reviews (100 per batch) with continuation tokens
- In-memory Excel generation using xlsxwriter

**Frontend Structure:**
- Single-page application using Bootstrap 5 and vanilla JavaScript
- Real-time progress tracking via EventSource API
- Chart.js for data visualization
- Flatpickr for date selection
- Theme switching with CSS custom properties

### Data Flow
1. Frontend sends review extraction request to `/api/reviews`
2. Backend streams data in JSON chunks: app details → reviews batches → progress → completion
3. Frontend accumulates reviews and updates UI in real-time
4. Export functionality sends accumulated data to `/api/export` for Excel generation

### Key Dependencies
- **google-play-scraper**: Core scraping functionality
- **xlsxwriter**: Excel export (in-memory generation)
- **Flask**: Web framework with streaming support
- **pandas/numpy**: Data processing (installed but minimal usage in current code)

### Error Handling Strategy
The application uses custom exceptions for different failure modes:
- AppNotFoundError: Invalid app ID
- NetworkError: Connection/API issues  
- DateRangeError: Invalid date inputs
- ScrapingError: Review extraction failures

### Filtering System
Reviews are filtered server-side by:
- Date range (start_date/end_date) - uses YYYY-MM-DD format
- Rating range (min_rating/max_rating) - 1-5 scale
- Keywords (comma-separated, case-insensitive)
- Country - supports various country codes (us, gb, ca, au, de, fr, jp, kr, cn, in, ph)

### Frontend Architecture
The single-page application uses:
- **Theme System**: CSS custom properties with data-theme attributes for dark/light modes
- **Real-time Updates**: EventSource API for streaming review data from server
- **Charts**: Chart.js for rating distribution (bar chart) and timeline visualization (line chart)
- **Date Picking**: Flatpickr for user-friendly date selection
- **State Management**: JavaScript maintains currentReviews array and pagination state

### API Endpoints
- `GET /` - Serves the main application page
- `POST /api/reviews` - Streams review data (Server-Sent Events)
- `POST /api/export` - Generates Excel file from review data
- `GET /test` - Health check endpoint

## File Structure
```
playstore-reviews-extraction/
├── app.py                 # Main Flask application with streaming endpoints
├── requirements.txt       # Python dependencies
├── static/
│   ├── css/style.css     # Responsive styles with CSS custom properties
│   └── js/theme.js       # Dark/light mode toggle with localStorage persistence
└── templates/
    └── index.html        # Single-page application with embedded JavaScript
```

## Important Implementation Details

### Port Management
The application automatically handles port conflicts by trying ports 5001-5010 sequentially. Port 5000 is deliberately skipped due to macOS AirPlay conflicts.

### Data Processing Flow
1. **Review Extraction**: Uses google-play-scraper with continuation tokens for paginated data
2. **Streaming**: Server yields JSON chunks for real-time frontend updates
3. **Filtering**: Applied server-side during extraction for efficiency
4. **Export**: In-memory Excel generation using xlsxwriter (no temporary files)

### Frontend State Management
- Reviews accumulated in `currentReviews` global array
- Charts updated incrementally as new data arrives
- Theme preference persisted in localStorage with system preference fallback